import '../models/chat_models.dart';
import '../models/tool_models.dart';

/// Base configuration class for all LLM providers
/// Provides common configuration options that all providers should support
abstract class ProviderConfig {
  /// API key for authentication with the provider
  final String apiKey;
  
  /// Base URL for API requests
  final String baseUrl;
  
  /// Model identifier/name to use
  final String model;
  
  /// Maximum tokens to generate in responses
  final int? maxTokens;
  
  /// Temperature parameter for controlling response randomness (0.0-2.0)
  final double? temperature;
  
  /// System prompt/context to guide model behavior
  final String? systemPrompt;
  
  /// Request timeout duration
  final Duration? timeout;
  
  /// Whether to enable streaming responses
  final bool stream;
  
  /// Top-p (nucleus) sampling parameter
  final double? topP;
  
  /// Top-k sampling parameter
  final int? topK;
  
  /// Function tools available to the model
  final List<Tool>? tools;
  
  /// Tool choice strategy
  final ToolChoice? toolChoice;
  
  /// Reasoning effort level for models that support it
  final ReasoningEffort? reasoningEffort;
  
  /// JSON schema for structured output
  final StructuredOutputFormat? jsonSchema;

  const ProviderConfig({
    required this.apiKey,
    required this.baseUrl,
    required this.model,
    this.maxTokens,
    this.temperature,
    this.systemPrompt,
    this.timeout,
    this.stream = false,
    this.topP,
    this.topK,
    this.tools,
    this.toolChoice,
    this.reasoningEffort,
    this.jsonSchema,
  });

  /// Create a copy of this config with updated values
  ProviderConfig copyWith({
    String? apiKey,
    String? baseUrl,
    String? model,
    int? maxTokens,
    double? temperature,
    String? systemPrompt,
    Duration? timeout,
    bool? stream,
    double? topP,
    int? topK,
    List<Tool>? tools,
    ToolChoice? toolChoice,
    ReasoningEffort? reasoningEffort,
    StructuredOutputFormat? jsonSchema,
  });

  /// Validate the configuration
  /// Throws [ArgumentError] if configuration is invalid
  void validate() {
    if (apiKey.isEmpty) {
      throw ArgumentError('API key cannot be empty');
    }
    if (baseUrl.isEmpty) {
      throw ArgumentError('Base URL cannot be empty');
    }
    if (model.isEmpty) {
      throw ArgumentError('Model cannot be empty');
    }
    if (temperature != null && (temperature! < 0.0 || temperature! > 2.0)) {
      throw ArgumentError('Temperature must be between 0.0 and 2.0');
    }
    if (topP != null && (topP! < 0.0 || topP! > 1.0)) {
      throw ArgumentError('Top-p must be between 0.0 and 1.0');
    }
    if (topK != null && topK! < 1) {
      throw ArgumentError('Top-k must be at least 1');
    }
    if (maxTokens != null && maxTokens! < 1) {
      throw ArgumentError('Max tokens must be at least 1');
    }
  }

  /// Convert reasoning effort to string for API calls
  String? get reasoningEffortValue => reasoningEffort?.value;

  /// Check if this provider supports reasoning
  bool get supportsReasoning => reasoningEffort != null;

  /// Get provider-specific headers
  Map<String, String> get headers;

  /// Get provider-specific default model
  String get defaultModel;

  /// Get provider-specific default base URL
  String get defaultBaseUrl;
}

/// Mixin for providers that support voice/TTS functionality
mixin VoiceCapability {
  /// Voice identifier for TTS
  String? get voice;
  
  /// Voice settings for TTS
  Map<String, dynamic>? get voiceSettings;
}

/// Mixin for providers that support embedding functionality
mixin EmbeddingCapability {
  /// Embedding encoding format
  String? get embeddingEncodingFormat;
  
  /// Embedding dimensions
  int? get embeddingDimensions;
}

/// Mixin for providers that support search functionality
mixin SearchCapability {
  /// Search parameters
  SearchParameters? get searchParameters;
}

/// Search parameters for providers that support search
class SearchParameters {
  final String? query;
  final int? maxResults;
  final Map<String, dynamic>? filters;

  const SearchParameters({
    this.query,
    this.maxResults,
    this.filters,
  });

  Map<String, dynamic> toJson() => {
    if (query != null) 'query': query,
    if (maxResults != null) 'max_results': maxResults,
    if (filters != null) 'filters': filters,
  };

  factory SearchParameters.fromJson(Map<String, dynamic> json) =>
      SearchParameters(
        query: json['query'] as String?,
        maxResults: json['max_results'] as int?,
        filters: json['filters'] as Map<String, dynamic>?,
      );
}
