import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:logging/logging.dart';

import 'chat_provider.dart';
import 'llm_error.dart';
import 'provider_config.dart';
import '../models/chat_models.dart';
import '../models/tool_models.dart';

/// Base class for all LLM providers
/// Provides common functionality and enforces consistent interface implementation
abstract class BaseProvider implements StreamingChatProvider, LLMProvider {
  /// Provider configuration
  final ProviderConfig config;
  
  /// HTTP client for API requests
  final Dio _dio;
  
  /// Logger instance for this provider
  final Logger _logger;

  BaseProvider(this.config, String loggerName) 
      : _dio = _createDio(config),
        _logger = Logger(loggerName) {
    config.validate();
  }

  /// Create configured Dio instance
  static Dio _createDio(ProviderConfig config) {
    return Dio(
      BaseOptions(
        baseUrl: config.baseUrl,
        connectTimeout: config.timeout ?? const Duration(seconds: 30),
        receiveTimeout: config.timeout ?? const Duration(seconds: 30),
        headers: config.headers,
      ),
    );
  }

  /// Get the provider name for logging and identification
  String get providerName;

  /// Get the API endpoint for chat completions
  String get chatEndpoint;

  /// Build request body for chat API
  Map<String, dynamic> buildChatRequest(
    List<ChatMessage> messages,
    List<Tool>? tools,
    bool stream,
  );

  /// Parse chat response from API
  ChatResponse parseChatResponse(Map<String, dynamic> response);

  /// Parse streaming chat events
  Stream<ChatStreamEvent> parseStreamEvents(Stream<String> stream);

  @override
  Future<ChatResponse> chat(List<ChatMessage> messages) async {
    return chatWithTools(messages, null);
  }

  @override
  Future<ChatResponse> chatWithTools(
    List<ChatMessage> messages,
    List<Tool>? tools,
  ) async {
    if (config.apiKey.isEmpty) {
      throw AuthError('Missing API key for $providerName');
    }

    try {
      final requestBody = buildChatRequest(messages, tools, false);

      // Log request at trace level
      if (_logger.isLoggable(Level.FINEST)) {
        _logger.finest('$providerName request payload: ${jsonEncode(requestBody)}');
      }

      _logger.fine('$providerName request: POST $chatEndpoint');

      var request = _dio.post(chatEndpoint, data: requestBody);

      // Add explicit timeout if configured
      if (config.timeout != null && config.timeout!.inSeconds > 0) {
        request = request.timeout(config.timeout!);
      }

      final response = await request;

      _logger.fine('$providerName HTTP status: ${response.statusCode}');

      if (response.statusCode != 200) {
        throw _handleHttpError(response.statusCode, response.data);
      }

      final responseData = response.data;
      if (responseData is! Map<String, dynamic>) {
        throw ResponseFormatError(
          'Invalid response format from $providerName API',
          responseData.toString(),
        );
      }

      return parseChatResponse(responseData);
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (e) {
      throw GenericError('Unexpected error in $providerName: $e');
    }
  }

  @override
  Stream<ChatStreamEvent> chatStream(
    List<ChatMessage> messages, {
    List<Tool>? tools,
  }) async* {
    if (config.apiKey.isEmpty) {
      yield ErrorEvent(AuthError('Missing API key for $providerName'));
      return;
    }

    try {
      final requestBody = buildChatRequest(messages, tools, true);

      // Log request at trace level
      if (_logger.isLoggable(Level.FINEST)) {
        _logger.finest(
          '$providerName stream request payload: ${jsonEncode(requestBody)}',
        );
      }

      _logger.fine('$providerName stream request: POST $chatEndpoint');

      final response = await _dio.post(
        chatEndpoint,
        data: requestBody,
        options: Options(responseType: ResponseType.stream),
      );

      _logger.fine('$providerName stream HTTP status: ${response.statusCode}');

      if (response.statusCode != 200) {
        yield ErrorEvent(_handleHttpError(response.statusCode, response.data));
        return;
      }

      final stream = response.data as ResponseBody;
      final stringStream = stream.stream.map(utf8.decode);

      await for (final event in parseStreamEvents(stringStream)) {
        yield event;
      }
    } on DioException catch (e) {
      _logger.severe('$providerName stream DioException: ${e.message}');
      yield ErrorEvent(_handleDioError(e));
    } catch (e) {
      _logger.severe('$providerName stream unexpected error: $e');
      yield ErrorEvent(GenericError('Unexpected error in $providerName: $e'));
    }
  }

  @override
  Future<List<ChatMessage>?> memoryContents() async => null;

  @override
  Future<String> summarizeHistory(List<ChatMessage> messages) async {
    final prompt =
        'Summarize in 2-3 sentences:\n${messages.map((m) => '${m.role.name}: ${m.content}').join('\n')}';
    final request = [ChatMessage.user(prompt)];
    final response = await chat(request);
    final text = response.text;
    if (text == null) {
      throw const GenericError('no text in summary response');
    }
    return text;
  }

  @override
  List<Tool>? get tools => config.tools;

  /// Handle HTTP errors with specific status codes
  LLMError _handleHttpError(int? statusCode, dynamic errorData) {
    switch (statusCode) {
      case 401:
        return AuthError('Invalid API key for $providerName');
      case 429:
        return const ProviderError('Rate limit exceeded');
      case 400:
        return ResponseFormatError(
          'Bad request - check your parameters',
          errorData?.toString() ?? '',
        );
      case 500:
        return ProviderError('$providerName server error');
      default:
        return ResponseFormatError(
          '$providerName API returned error status: $statusCode',
          errorData?.toString() ?? '',
        );
    }
  }

  /// Handle Dio exceptions
  LLMError _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkError('Request timeout for $providerName: ${e.message}');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        return _handleHttpError(statusCode, e.response?.data);
      case DioExceptionType.connectionError:
        return NetworkError('Connection error for $providerName: ${e.message}');
      case DioExceptionType.cancel:
        return const GenericError('Request was cancelled');
      default:
        return GenericError('Network error for $providerName: ${e.message}');
    }
  }

  // Default implementations for LLMProvider interface
  // Subclasses should override these if they support the functionality

  @override
  Future<CompletionResponse> complete(CompletionRequest request) async {
    throw const GenericError('Completion not supported by this provider');
  }

  @override
  Future<List<List<double>>> embed(List<String> input) async {
    throw const GenericError('Embeddings not supported by this provider');
  }

  @override
  Future<String> transcribe(List<int> audio) async {
    throw const GenericError('Speech-to-text not supported by this provider');
  }

  @override
  Future<String> transcribeFile(String filePath) async {
    throw const GenericError('Speech-to-text not supported by this provider');
  }

  @override
  Future<List<int>> speech(String text) async {
    throw const GenericError('Text-to-speech not supported by this provider');
  }

  @override
  Future<List<AIModel>> models() async {
    throw const GenericError('Model listing not supported by this provider');
  }
}
