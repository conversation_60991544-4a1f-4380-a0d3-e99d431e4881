import 'dart:convert';
import 'dart:async';
import 'package:dio/dio.dart';
import 'package:logging/logging.dart';

import '../core/base_provider.dart';
import '../core/chat_provider.dart';
import '../core/llm_error.dart';
import '../core/provider_config.dart';
import '../models/chat_models.dart';
import '../models/tool_models.dart';
import '../utils/reasoning_utils.dart';

/// OpenAI provider configuration
class OpenAIConfig extends ProviderConfig
    with VoiceCapability, EmbeddingCapability {
  @override
  final String? voice;

  @override
  final String? embeddingEncodingFormat;

  @override
  final int? embeddingDimensions;

  @override
  Map<String, dynamic>? get voiceSettings => null;

  @override
  Map<String, String> get headers => {
    'Authorization': 'Bearer $apiKey',
    'Content-Type': 'application/json',
  };

  @override
  String get defaultModel => 'gpt-3.5-turbo';

  @override
  String get defaultBaseUrl => 'https://api.openai.com/v1/';

  const OpenAIConfig({
    required super.apiKey,
    super.baseUrl = 'https://api.openai.com/v1/',
    super.model = 'gpt-3.5-turbo',
    super.maxTokens,
    super.temperature,
    super.systemPrompt,
    super.timeout,
    super.stream = false,
    super.topP,
    super.topK,
    super.tools,
    super.toolChoice,
    super.reasoningEffort,
    super.jsonSchema,
    this.voice,
    this.embeddingEncodingFormat,
    this.embeddingDimensions,
  });

  @override
  OpenAIConfig copyWith({
    String? apiKey,
    String? baseUrl,
    String? model,
    int? maxTokens,
    double? temperature,
    String? systemPrompt,
    Duration? timeout,
    bool? stream,
    double? topP,
    int? topK,
    List<Tool>? tools,
    ToolChoice? toolChoice,
    ReasoningEffort? reasoningEffort,
    StructuredOutputFormat? jsonSchema,
    String? voice,
    String? embeddingEncodingFormat,
    int? embeddingDimensions,
  }) => OpenAIConfig(
    apiKey: apiKey ?? this.apiKey,
    baseUrl: baseUrl ?? this.baseUrl,
    model: model ?? this.model,
    maxTokens: maxTokens ?? this.maxTokens,
    temperature: temperature ?? this.temperature,
    systemPrompt: systemPrompt ?? this.systemPrompt,
    timeout: timeout ?? this.timeout,
    stream: stream ?? this.stream,
    topP: topP ?? this.topP,
    topK: topK ?? this.topK,
    tools: tools ?? this.tools,
    toolChoice: toolChoice ?? this.toolChoice,
    reasoningEffort: reasoningEffort ?? this.reasoningEffort,
    jsonSchema: jsonSchema ?? this.jsonSchema,
    voice: voice ?? this.voice,
    embeddingEncodingFormat:
        embeddingEncodingFormat ?? this.embeddingEncodingFormat,
    embeddingDimensions: embeddingDimensions ?? this.embeddingDimensions,
  );
}

/// OpenAI chat response implementation
class OpenAIChatResponse implements ChatResponse {
  final Map<String, dynamic> _rawResponse;
  final String? _thinkingContent;

  OpenAIChatResponse(this._rawResponse, [this._thinkingContent]);

  @override
  String? get text {
    final choices = _rawResponse['choices'] as List?;
    if (choices == null || choices.isEmpty) return null;

    final message = choices.first['message'] as Map<String, dynamic>?;
    return message?['content'] as String?;
  }

  @override
  List<ToolCall>? get toolCalls {
    final choices = _rawResponse['choices'] as List?;
    if (choices == null || choices.isEmpty) return null;

    final message = choices.first['message'] as Map<String, dynamic>?;
    final toolCalls = message?['tool_calls'] as List?;

    if (toolCalls == null) return null;

    return toolCalls
        .map((tc) => ToolCall.fromJson(tc as Map<String, dynamic>))
        .toList();
  }

  @override
  UsageInfo? get usage {
    final usageData = _rawResponse['usage'] as Map<String, dynamic>?;
    if (usageData == null) return null;

    return UsageInfo.fromJson(usageData);
  }

  @override
  String? get thinking => _thinkingContent;

  @override
  String toString() {
    final textContent = text;
    final calls = toolCalls;

    if (textContent != null && calls != null) {
      return '${calls.map((c) => c.toString()).join('\n')}\n$textContent';
    } else if (textContent != null) {
      return textContent;
    } else if (calls != null) {
      return calls.map((c) => c.toString()).join('\n');
    } else {
      return '';
    }
  }
}

/// OpenAI provider implementation
class OpenAIProvider extends BaseProvider {
  OpenAIProvider(OpenAIConfig config) : super(config, 'OpenAIProvider');

  @override
  String get providerName => 'OpenAI';

  @override
  String get chatEndpoint => 'chat/completions';

  /// Get OpenAI-specific config
  OpenAIConfig get openAIConfig => config as OpenAIConfig;

  @override
  Map<String, dynamic> buildChatRequest(
    List<ChatMessage> messages,
    List<Tool>? tools,
    bool stream,
  ) {
    final apiMessages = <Map<String, dynamic>>[];

    // Add system message if configured
    if (config.systemPrompt != null) {
      apiMessages.add({'role': 'system', 'content': config.systemPrompt});
    }

    // Convert messages to OpenAI format
    for (final message in messages) {
      if (message.messageType is ToolResultMessage) {
        // Handle tool results as separate messages
        final toolResults = (message.messageType as ToolResultMessage).results;
        for (final result in toolResults) {
          apiMessages.add({
            'role': 'tool',
            'tool_call_id': result.id,
            'content': result.function.arguments.isNotEmpty
                ? result.function.arguments
                : message.content,
          });
        }
      } else {
        apiMessages.add(_convertMessage(message));
      }
    }

    final body = <String, dynamic>{
      'model': config.model,
      'messages': apiMessages,
      'stream': stream,
    };

    // Add optional parameters using reasoning utils
    body.addAll(
      ReasoningUtils.getMaxTokensParams(
        model: config.model,
        maxTokens: config.maxTokens,
      ),
    );

    // Add temperature if not disabled for reasoning models
    if (config.temperature != null &&
        !ReasoningUtils.shouldDisableTemperature(config.model)) {
      body['temperature'] = config.temperature;
    }

    // Add top_p if not disabled for reasoning models
    if (config.topP != null &&
        !ReasoningUtils.shouldDisableTopP(config.model)) {
      body['top_p'] = config.topP;
    }
    if (config.topK != null) body['top_k'] = config.topK;

    // Add reasoning effort parameters
    final providerId = _getProviderId();
    body.addAll(
      ReasoningUtils.getReasoningEffortParams(
        providerId: providerId,
        model: config.model,
        reasoningEffort: config.reasoningEffortValue,
      ),
    );

    // Add tools if provided
    final effectiveTools = tools ?? config.tools;
    if (effectiveTools != null && effectiveTools.isNotEmpty) {
      body['tools'] = effectiveTools.map((t) => t.toJson()).toList();

      final effectiveToolChoice = config.toolChoice;
      if (effectiveToolChoice != null) {
        body['tool_choice'] = effectiveToolChoice.toJson();
      }
    }

    // Add structured output if configured
    if (config.jsonSchema != null) {
      final schema = config.jsonSchema!;
      final responseFormat = <String, dynamic>{
        'type': 'json_schema',
        'json_schema': schema.toJson(),
      };

      // Ensure additionalProperties is set to false for OpenAI compliance
      if (schema.schema != null) {
        final schemaMap = Map<String, dynamic>.from(schema.schema!);
        if (!schemaMap.containsKey('additionalProperties')) {
          schemaMap['additionalProperties'] = false;
        }
        responseFormat['json_schema']['schema'] = schemaMap;
      }

      body['response_format'] = responseFormat;
    }

    return body;
  }

  @override
  ChatResponse parseChatResponse(Map<String, dynamic> response) {
    // Extract thinking/reasoning content from non-streaming response
    String? thinkingContent;

    // Check for reasoning content in the response
    final choices = response['choices'] as List?;
    if (choices != null && choices.isNotEmpty) {
      final choice = choices.first as Map<String, dynamic>;
      final message = choice['message'] as Map<String, dynamic>?;

      if (message != null) {
        // Check for reasoning content in various possible fields
        thinkingContent =
            message['reasoning'] as String? ??
            message['thinking'] as String? ??
            message['reasoning_content'] as String?;

        // For models that use <think> tags, extract thinking content
        final content = message['content'] as String?;
        if (content != null && ReasoningUtils.containsThinkingTags(content)) {
          final thinkMatch = RegExp(
            r'<think>(.*?)</think>',
            dotAll: true,
          ).firstMatch(content);
          if (thinkMatch != null) {
            thinkingContent = thinkMatch.group(1)?.trim();
            // Update the message content to remove thinking tags for clean response
            message['content'] = ReasoningUtils.filterThinkingContent(content);
          }
        }
      }
    }

    return OpenAIChatResponse(response, thinkingContent);
  }

  @override
  Stream<ChatStreamEvent> parseStreamEvents(Stream<String> stream) async* {
    // Reasoning tracking variables
    bool hasReasoningContent = false;
    String lastChunk = '';
    final thinkingBuffer = StringBuffer();

    await for (final chunk in stream) {
      final lines = chunk.split('\n');
      for (final line in lines) {
        if (line.startsWith('data: ')) {
          final data = line.substring(6).trim();
          if (data == '[DONE]') {
            return;
          }

          try {
            final json = jsonDecode(data) as Map<String, dynamic>;

            final events = _parseStreamEventWithReasoning(
              json,
              hasReasoningContent,
              lastChunk,
              thinkingBuffer,
            );

            // Update tracking variables using reasoning utils
            final deltaObj = _getDeltaObject(json);
            if (deltaObj != null) {
              final reasoningResult = ReasoningUtils.checkReasoningStatus(
                delta: deltaObj,
                hasReasoningContent: hasReasoningContent,
                lastChunk: lastChunk,
              );
              hasReasoningContent = reasoningResult.hasReasoningContent;
              lastChunk = reasoningResult.updatedLastChunk;
            }

            for (final event in events) {
              yield event;
            }
          } catch (e) {
            // Skip malformed JSON chunks
            continue;
          }
        }
      }
    }
  }

  /// Parse stream event with reasoning support
  List<ChatStreamEvent> _parseStreamEventWithReasoning(
    Map<String, dynamic> json,
    bool hasReasoningContent,
    String lastChunk,
    StringBuffer thinkingBuffer,
  ) {
    final events = <ChatStreamEvent>[];

    final choices = json['choices'] as List?;
    if (choices == null || choices.isEmpty) return events;

    final choice = choices.first as Map<String, dynamic>;
    final delta = choice['delta'] as Map<String, dynamic>?;
    if (delta == null) return events;

    // Handle text content
    final content = delta['content'] as String?;
    if (content != null) {
      // Filter out thinking tags for models that use <think> tags
      final filteredContent = ReasoningUtils.filterThinkingContent(content);

      if (filteredContent.isNotEmpty) {
        events.add(TextDeltaEvent(filteredContent));
      }
    }

    // Handle reasoning content
    final reasoningContent = ReasoningUtils.extractReasoningContent(delta);
    if (reasoningContent != null) {
      events.add(ThinkingDeltaEvent(reasoningContent));
    }

    // Handle tool calls
    final toolCalls = delta['tool_calls'] as List?;
    if (toolCalls != null) {
      for (final toolCall in toolCalls) {
        try {
          events.add(ToolCallDeltaEvent(ToolCall.fromJson(toolCall)));
        } catch (e) {
          // Skip malformed tool calls
        }
      }
    }

    return events;
  }

  /// Get delta object from stream event
  Map<String, dynamic>? _getDeltaObject(Map<String, dynamic> json) {
    final choices = json['choices'] as List?;
    if (choices == null || choices.isEmpty) return null;

    final choice = choices.first as Map<String, dynamic>;
    return choice['delta'] as Map<String, dynamic>?;
  }

  /// Get provider ID based on base URL
  String _getProviderId() {
    final baseUrl = config.baseUrl.toLowerCase();
    if (baseUrl.contains('openrouter')) {
      return 'openrouter';
    } else if (baseUrl.contains('groq')) {
      return 'groq';
    } else if (baseUrl.contains('deepseek')) {
      return 'deepseek';
    } else if (baseUrl.contains('openai')) {
      return 'openai';
    } else {
      return 'openai'; // Default fallback
    }
  }

  Map<String, dynamic> _convertMessage(ChatMessage message) {
    switch (message.messageType) {
      case TextMessage():
        return {'role': message.role.name, 'content': message.content};
      case ImageMessage(mime: final mime, data: final data):
        return {
          'role': message.role.name,
          'content': [
            if (message.content.isNotEmpty)
              {'type': 'text', 'text': message.content},
            {
              'type': 'image_url',
              'image_url': {
                'url': 'data:${mime.mimeType};base64,${base64Encode(data)}',
              },
            },
          ],
        };
      case ImageUrlMessage(url: final url):
        return {
          'role': message.role.name,
          'content': [
            if (message.content.isNotEmpty)
              {'type': 'text', 'text': message.content},
            {
              'type': 'image_url',
              'image_url': {'url': url},
            },
          ],
        };
      case ToolUseMessage(toolCalls: final toolCalls):
        return {
          'role': 'assistant',
          'tool_calls': toolCalls.map((tc) => tc.toJson()).toList(),
        };
      default:
        return {'role': message.role.name, 'content': message.content};
    }
  }
}
